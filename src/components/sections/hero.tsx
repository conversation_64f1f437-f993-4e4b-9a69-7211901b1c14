"use client";

import { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import { ChevronRight, Star, Sparkles, Users, TrendingUp } from 'lucide-react';

export function Hero() {
  const [isHovered, setIsHovered] = useState(false);
  const [activeTab, setActiveTab] = useState('expired');
  const [revenueCount, setRevenueCount] = useState(0);
  const [hasAnimated, setHasAnimated] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  const badgeRef = useRef<HTMLDivElement>(null);

  // Reset animation state when page becomes visible (handles page reload/navigation)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && hasAnimated) {
        // Reset animation state when page becomes visible again
        setHasAnimated(false);
        setIsAnimating(false);
        setRevenueCount(0);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [hasAnimated]);

  // Also reset on component mount (handles page refresh)
  useEffect(() => {
    setHasAnimated(false);
    setIsAnimating(false);
    setRevenueCount(0);
  }, []);

  // Revenue counter animation with Intersection Observer
  useEffect(() => {
    const currentRef = badgeRef.current;

    const startAnimation = () => {
      if (hasAnimated || isAnimating) return; // Prevent multiple animations

      setIsAnimating(true);
      const target = 3500000;
      const duration = 2500; // Slightly slower for better visibility
      const increment = target / (duration / 16);

      let current = 0;
      const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
          setRevenueCount(target);
          clearInterval(timer);
          setHasAnimated(true);
          setIsAnimating(false);
        } else {
          setRevenueCount(Math.floor(current));
        }
      }, 16);

      return timer;
    };

    const observer = new IntersectionObserver(
      (entries) => {
        const entry = entries[0];
        if (entry.isIntersecting && !hasAnimated) {
          // Add a small delay to make the animation more noticeable
          setTimeout(() => {
            startAnimation();
          }, 500);
        }
      },
      { threshold: 0.1 }
    );

    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [hasAnimated, isAnimating]);

  const renderContent = () => {
    switch (activeTab) {
      case 'expired':
        return (
          <div className="bg-gray-900/90 backdrop-blur p-6 rounded-xl">
            <h3 className="font-bold text-purple-300 text-sm mb-2 flex items-center gap-2">
              <span className="text-lg">♻️</span> FOLLOW YOUR EXPIRED FANS
            </h3>
            <p className="text-gray-400 text-xs mb-4 leading-relaxed">
              Follow your expired fans through this extension, so you&apos;ll keep the ability to message / sell PPV even after they expire or unsubscribe.
            </p>
            <div className="bg-black/40 rounded-lg p-4 space-y-4 border border-purple-500/20">
              <div className="flex items-start gap-3">
                <span className="text-green-400 text-xl">✅</span>
                <div>
                  <p className="text-sm text-gray-300 font-medium">
                    You can follow <strong className="text-purple-400">505</strong> out of <strong className="text-purple-400">700</strong> expired fans.
                  </p>
                  <p className="text-xs text-gray-500 mt-1">
                    Feel free to re-scan expired fans with the button below.
                  </p>
                </div>
              </div>
              
              <div className="mt-3">
                <label className="block text-gray-400 text-xs font-medium mb-2">After following, add fans to a list:</label>
                <div className="relative flex items-center">
                  <select className="w-full px-3 py-2 bg-black/60 border border-purple-500/30 rounded-lg text-gray-300 text-sm focus:outline-none focus:border-purple-500/50 appearance-none pr-10">
                    <option>-- Don&apos;t add to any list --</option>
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <svg className="h-4 w-4 text-gray-500" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" /></svg>
                  </div>
                  <button className="ml-2 p-2 text-purple-400 hover:bg-purple-500/20 rounded-lg transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                        <path fillRule="evenodd" d="M8 3a5 5 0 1 0 4.546 2.914.5.5 0 0 1 .908-.417A6 6 0 1 1 8 2v1z"/>
                        <path d="M8 4.466V.534a.25.25 0 0 1 .41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 0 1 8 4.466z"/>
                    </svg>
                  </button>
                </div>
              </div>
              
              <div>
                <button className="bg-purple-600/30 text-gray-400 font-semibold py-2.5 px-5 rounded-full text-xs uppercase tracking-wide cursor-not-allowed">
                  FOLLOW 505 EXPIRED FANS
                </button>
              </div>
            </div>
            
            <div className="mt-4 space-y-2">
              <button className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-semibold py-2.5 px-5 rounded-full transition-all flex items-center gap-2 text-xs uppercase tracking-wide shadow-lg">
                  <svg 
                    xmlns="http://www.w3.org/2000/svg"
                    width="14"
                    height="14"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M3 7V5a2 2 0 012-2h2" />
                    <path d="M17 3h2a2 2 0 012 2v2" />
                    <path d="M21 17v2a2 2 0 01-2 2h-2" />
                    <path d="M7 21H5a2 2 0 01-2-2v-2" />
                    <line x1="7" y1="12" x2="17" y2="12" />
                  </svg>
                  SCAN ALL EXPIRED FANS
              </button>
              <p className="text-xs text-gray-500 pl-1">
                Never scanned
              </p>
            </div>
          </div>
        );
      case 'current':
        return (
          <div className="bg-gray-900/90 backdrop-blur p-6 rounded-xl">
            <h3 className="font-bold text-purple-300 text-sm mb-2 flex items-center gap-2">
              <span className="text-lg">💖</span> FOLLOW YOUR CURRENT FANS
            </h3>
            <p className="text-gray-400 text-xs mb-4 leading-relaxed">
              Follow your current fans through this extension, so you&apos;ll keep the ability to message / sell PPV even after they expire or unsubscribe.
            </p>
            
            <div className="bg-black/40 rounded-lg p-4 space-y-4 border border-purple-500/20">
              <div className="flex items-start gap-3">
                <span className="text-green-400 text-xl">✅</span>
                <div>
                  <p className="text-sm text-gray-300 font-medium">
                    You can follow <strong className="text-purple-400">207</strong> out of <strong className="text-purple-400">500</strong> current fans.
                  </p>
                </div>
              </div>
              
              <div className="flex items-center">
                <input id="skip-rebill" type="checkbox" className="h-4 w-4 rounded border-purple-500/30 bg-black/60 text-purple-600 focus:ring-purple-500 focus:ring-offset-0" />
                <label htmlFor="skip-rebill" className="ml-2 block text-sm font-medium text-gray-300">
                  Skip following fans with REBILL turned on?
                </label>
              </div>
              
              <div>
                <label className="block text-gray-400 text-xs font-medium mb-2">After following, add fans to list:</label>
                <div className="relative flex items-center">
                  <select className="w-full px-3 py-2 bg-black/60 border border-purple-500/30 rounded-lg text-gray-300 text-sm focus:outline-none focus:border-purple-500/50 appearance-none pr-10">
                    <option>-- Don&apos;t add to any list --</option>
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                    <svg className="h-4 w-4 text-gray-500" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" /></svg>
                  </div>
                  <button className="ml-2 p-2 text-purple-400 hover:bg-purple-500/20 rounded-lg transition-colors">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                        <path fillRule="evenodd" d="M8 3a5 5 0 1 0 4.546 2.914.5.5 0 0 1 .908-.417A6 6 0 1 1 8 2v1z"/>
                        <path d="M8 4.466V.534a.25.25 0 0 1 .41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 0 1 8 4.466z"/>
                    </svg>
                  </button>
                </div>
              </div>
              
              <div>
                <button className="bg-purple-600/30 text-gray-400 font-semibold py-2.5 px-5 rounded-full text-xs uppercase tracking-wide cursor-not-allowed">
                  FOLLOW 207 CURRENT FANS
                </button>
              </div>
            </div>
            
            <div className="mt-4 space-y-2">
              <button className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-semibold py-2.5 px-5 rounded-full transition-all flex items-center gap-2 text-xs uppercase tracking-wide shadow-lg">
                <svg 
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="14"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M3 7V5a2 2 0 012-2h2" />
                  <path d="M17 3h2a2 2 0 012 2v2" />
                  <path d="M21 17v2a2 2 0 01-2 2h-2" />
                  <path d="M7 21H5a2 2 0 01-2-2v-2" />
                  <line x1="7" y1="12" x2="17" y2="12" />
                </svg>
                SCAN ALL CURRENT FANS
              </button>
              
              <p className="text-xs text-gray-500 pl-1">
                Never scanned
              </p>
            </div>
          </div>
        );
      case 'new':
        return (
          <div className="bg-gray-900/90 backdrop-blur p-6 rounded-xl">
            <h3 className="font-bold text-purple-300 text-sm mb-2 flex items-center gap-2">
              <span className="text-lg">🤖</span> AUTOFOLLOW BOT
            </h3>
             <p className="text-gray-400 text-xs mb-4 leading-relaxed">
              Follow people who are subscribed to other creators and registered on OnlyFans just recently. Be one of the first accounts they&apos;ll ever see ✨
            </p>
            
            <div className="bg-black/40 rounded-lg p-4 space-y-4 border border-purple-500/20">
                <div>
                    <label className="block text-gray-400 text-xs font-medium mb-2">How many people do you want to follow?</label>
                    <div className="relative">
                        <select className="w-full px-3 py-2 bg-black/60 border border-purple-500/30 rounded-lg text-gray-300 text-sm focus:outline-none focus:border-purple-500/50 appearance-none pr-10">
                            <option>Follow 100 people (Recommended because of OF limits)</option>
                        </select>
                        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                            <svg className="h-4 w-4 text-gray-500" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" /></svg>
                        </div>
                    </div>
                </div>
                <div>
                    <label className="block text-gray-400 text-xs font-medium mb-2">After following, add fans to a list:</label>
                    <div className="relative flex items-center">
                        <select className="w-full px-3 py-2 bg-black/60 border border-purple-500/30 rounded-lg text-gray-300 text-sm focus:outline-none focus:border-purple-500/50 appearance-none pr-10">
                            <option>-- Don&apos;t add to any list --</option>
                        </select>
                        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                           <svg className="h-4 w-4 text-gray-500" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" /></svg>
                        </div>
                         <button className="ml-2 p-2 text-purple-400 hover:bg-purple-500/20 rounded-lg transition-colors">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                <path fillRule="evenodd" d="M8 3a5 5 0 1 0 4.546 2.914.5.5 0 0 1 .908-.417A6 6 0 1 1 8 2v1z"/>
                                <path d="M8 4.466V.534a.25.25 0 0 1 .41-.192l2.36 1.966c.12.1.12.284 0 .384L8.41 4.658A.25.25 0 0 1 8 4.466z"/>
                            </svg>
                        </button>
                    </div>
                </div>
                <p className="text-xs text-gray-500">
                    Don&apos;t worry, you will only follow FREE accounts, so you won&apos;t get charged anything.
                </p>
                <p className="text-xs text-gray-500">
                    All fans are ethically sourced from OnlyFans database using our own AI.
                </p>
                <div>
                  <button className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-semibold py-2.5 px-5 rounded-full transition-all text-xs uppercase tracking-wide shadow-lg">
                      START FOLLOWING
                  </button>
                </div>
            </div>
          </div>
        );
      case 'messaging':
        return (
          <div className="bg-gray-900/90 backdrop-blur p-6 rounded-xl">
            <h3 className="font-bold text-purple-300 text-sm mb-2 flex items-center gap-2">
              <span className="text-lg">💬</span> PERSONALIZED PRIORITY MASS MESSAGE
            </h3>
            <p className="text-gray-400 text-xs mb-4 leading-relaxed">
             Gain unfair advantage over other creators - Personalized mass message shows up on the #1 position in the fan&apos;s chat inbox.
            </p>
            <button className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-semibold py-2.5 px-5 rounded-full transition-all flex items-center justify-center gap-2 text-xs uppercase tracking-wide shadow-lg">
              <svg 
                fill="#ffffff" 
                version="1.1" 
                id="Capa_1" 
                xmlns="http://www.w3.org/2000/svg" 
                xmlnsXlink="http://www.w3.org/1999/xlink" 
                width="16" 
                height="16" 
                viewBox="0 0 38.342 38.342" 
                xmlSpace="preserve" 
                stroke="#ffffff"
              >
                <g id="SVGRepo_bgCarrier" strokeWidth="0"/>
                <g id="SVGRepo_tracerCarrier" strokeLinecap="round" strokeLinejoin="round"/>
                <g id="SVGRepo_iconCarrier"> 
                  <g> 
                    <path d="M19.171,0C8.6,0,0,8.6,0,19.171C0,29.74,8.6,38.342,19.171,38.342c10.569,0,19.171-8.602,19.171-19.171 C38.342,8.6,29.74,0,19.171,0z M19.171,34.341C10.806,34.341,4,27.533,4,19.17c0-8.365,6.806-15.171,15.171-15.171 s15.171,6.806,15.171,15.171C34.342,27.533,27.536,34.341,19.171,34.341z M30.855,19.171c0,1.656-1.344,3-3,3h-5.685v5.685 c0,1.655-1.345,3-3,3c-1.657,0-3-1.345-3-3v-5.685h-5.684c-1.657,0-3-1.344-3-3c0-1.657,1.343-3,3-3h5.684v-5.683 c0-1.657,1.343-3,3-3c1.655,0,3,1.343,3,3v5.683h5.685C29.512,16.171,30.855,17.514,30.855,19.171z"/> 
                  </g> 
                </g>
              </svg>
              NEW PRIORITY MASS MESSAGE
            </button>
            <div className="mt-6">
              <h4 className="font-bold text-gray-300 text-sm mb-2">HISTORY</h4>
              <p className="text-gray-500 text-xs">
                You haven&apos;t sent any personalized priority mass messages yet
              </p>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <section className="hero-gradient min-h-[90vh] flex items-center relative overflow-hidden pt-20 md:pt-32">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/4 -left-48 w-96 h-96 bg-purple-600 rounded-full mix-blend-multiply filter blur-[120px] opacity-20 animate-pulse"></div>
        <div className="absolute top-1/2 -right-48 w-96 h-96 bg-pink-600 rounded-full mix-blend-multiply filter blur-[120px] opacity-20 animate-pulse delay-1000"></div>
        <div className="absolute -bottom-48 left-1/2 w-96 h-96 bg-purple-500 rounded-full mix-blend-multiply filter blur-[120px] opacity-20 animate-pulse delay-2000"></div>
      </div>

      <div className="relative max-w-7xl mx-auto px-6 lg:px-8 w-full">
        <div className="flex flex-col lg:flex-row items-center justify-between gap-12">
          {/* Left Content */}
          <div className="flex-1 text-left max-w-2xl lg:pl-8 lg:pr-16">
            {/* Badge */}
            <div
              ref={badgeRef}
              className="inline-flex items-center gap-2 px-4 py-2 rounded-full glass-effect text-sm font-medium text-purple-300 mb-8 border border-purple-500/30"
            >
              <Sparkles className="w-4 h-4" />
              ${revenueCount.toLocaleString()} of revenue unlocked
            </div>

            {/* Main heading */}
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 tracking-tight leading-tight" style={{animationDelay: '0.2s'}}>
              Maximize your <span className="gradient-text">OnlyFans reach</span>,
              <br />
              Recapture <span className="gradient-text">premium subscribers</span>
            </h1>

            {/* Subheading */}
            <p className="text-lg md:text-xl text-gray-300 mb-8 leading-relaxed max-w-xl" style={{animationDelay: '0.4s'}}>
              Auto-follow new OF users and reconnect with expired VIP fans. Cross-promote between your accounts for explosive growth 🚀
            </p>

            {/* CTA buttons */}
            <div className="flex flex-col sm:flex-row gap-4 items-start mb-8" style={{animationDelay: '0.6s'}}>
              <a 
                href="https://chrome.google.com/webstore/detail/follow-bot-for-onlyfans-e/plklbjmdembgfdaafefofjllkgfpjepf" 
                target="_blank"
                className="group premium-button text-white font-semibold px-8 py-4 rounded-full inline-flex items-center gap-3 shadow-neon hover-lift"
                onMouseEnter={() => setIsHovered(true)}
                onMouseLeave={() => setIsHovered(false)}
              >
                <svg xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" viewBox="0 0 48 48" height="24" width="24" className="w-6 h-6">
                  <defs>
                    <linearGradient id="a" x1="3.2173" y1="15" x2="44.7812" y2="15" gradientUnits="userSpaceOnUse">
                      <stop offset="0" stopColor="#d93025"/>
                      <stop offset="1" stopColor="#ea4335"/>
                    </linearGradient>
                    <linearGradient id="b" x1="20.7219" y1="47.6791" x2="41.5039" y2="11.6837" gradientUnits="userSpaceOnUse">
                      <stop offset="0" stopColor="#fcc934"/>
                      <stop offset="1" stopColor="#fbbc04"/>
                    </linearGradient>
                    <linearGradient id="c" x1="26.5981" y1="46.5015" x2="5.8161" y2="10.506" gradientUnits="userSpaceOnUse">
                      <stop offset="0" stopColor="#1e8e3e"/>
                      <stop offset="1" stopColor="#34a853"/>
                    </linearGradient>
                  </defs>
                  <circle cx="24" cy="23.9947" r="12" style={{fill:'#fff'}}/>
                  <path d="M3.2154,36A24,24,0,1,0,12,3.2154,24,24,0,0,0,3.2154,36ZM34.3923,18A12,12,0,1,1,18,13.6077,12,12,0,0,1,34.3923,18Z" style={{fill:'none'}}/>
                  <path d="M24,12H44.7812a23.9939,23.9939,0,0,0-41.5639.0029L13.6079,30l.0093-.0024A11.9852,11.9852,0,0,1,24,12Z" style={{fill:'url(#a)'}}/>
                  <circle cx="24" cy="24" r="9.5" style={{fill:'#1a73e8'}}/>
                  <path d="M34.3913,30.0029,24.0007,48A23.994,23.994,0,0,0,44.78,12.0031H23.9989l-.0025.0093A11.985,11.985,0,0,1,34.3913,30.0029Z" style={{fill:'url(#b)'}}/>
                  <path d="M13.6086,30.0031,3.218,12.006A23.994,23.994,0,0,0,24.0025,48L34.3931,30.0029l-.0067-.0068a11.9852,11.9852,0,0,1-20.7778.007Z" style={{fill:'url(#c)'}}/>
                </svg>
                Add to Chrome
                <ChevronRight className={`w-5 h-5 transition-transform duration-200 ${isHovered ? 'translate-x-1' : ''}`} />
              </a>
            </div>

            {/* Social proof */}
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-6 text-gray-400" style={{animationDelay: '0.8s'}}>
              <div className="flex items-center gap-2">
                <div className="flex text-yellow-400">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 fill-current" />
                  ))}
                </div>
                <span className="text-sm font-medium whitespace-nowrap text-gray-300">4.9/5 rating</span>
              </div>
              
              <div className="flex items-center gap-2">
                <Users className="w-5 h-5 text-purple-400" />
                <span className="text-sm font-medium text-gray-300">10,000+ creators</span>
              </div>
              
              <div className="flex items-center gap-2">
                <TrendingUp className="w-5 h-5 text-purple-400" />
                <span className="text-sm font-medium text-gray-300">#1 Growth Tool</span>
              </div>
            </div>
          </div>

          {/* Right Content - Product Interface */}
          <div className="flex-1 hidden lg:flex items-start justify-center lg:justify-end mt-8">
            <div className="relative">
              {/* Floating UI Demo */}
              <div className="transform scale-[0.85]">
                <div className="relative animate-float">
                  <span className="absolute -top-8 left-1/2 transform -translate-x-1/2 text-sm text-purple-400/60 font-medium uppercase tracking-wider">Live Demo</span>
                  <div className="bg-gray-900/95 backdrop-blur-xl rounded-2xl shadow-2xl overflow-hidden border border-purple-500/20 neon-border" style={{ width: '550px', height: '550px' }}>
                    {/* Browser header */}
                    <div className="bg-black/50 px-4 py-3 flex items-center gap-2 border-b border-purple-500/20">
                      <div className="flex gap-1.5">
                        <div className="w-3 h-3 rounded-full bg-red-500/80"></div>
                        <div className="w-3 h-3 rounded-full bg-yellow-500/80"></div>
                        <div className="w-3 h-3 rounded-full bg-green-500/80"></div>
                      </div>
                      <div className="flex-1 mx-4">
                        <div className="bg-gray-800/60 rounded-lg px-3 py-1 text-sm text-gray-400">onlyfans.com</div>
                      </div>
                    </div>
                    {/* Interface content */}
                    <div className="p-5 bg-gray-900/50">
                      {/* Header */}
                      <div className="flex items-center gap-3 mb-5">
                        <Image 
                          src="/images/demo-avatar.jpg" 
                          alt="Demo avatar" 
                          width={48}
                          height={48}
                          className="w-12 h-12 rounded-full object-cover ring-2 ring-purple-500/50"
                        />
                        <div>
                          <div className="font-semibold text-white">@creator_pro</div>
                          <div className="text-sm text-gray-400 flex items-center gap-1">
                            <span className="text-purple-400">💎</span>
                            250,000 credits
                          </div>
                        </div>
                        <a href="#" className="ml-auto text-purple-400 text-sm hover:text-purple-300 transition-colors">Buy credits</a>
                      </div>
                      
                      {/* Tabs */}
                      <div className="flex mb-0 relative">
                        <button 
                          onClick={() => setActiveTab('expired')}
                          className={`px-4 py-2.5 text-xs font-semibold flex items-center gap-1.5 transition-all ${
                            activeTab === 'expired' 
                              ? 'text-purple-400 border-b-2 border-purple-400' 
                              : 'text-gray-500 hover:text-gray-300'
                          }`}
                        >
                          <span>♻️</span> EXPIRED
                        </button>
                        <button 
                          onClick={() => setActiveTab('current')}
                          className={`px-4 py-2.5 text-xs font-semibold flex items-center gap-1.5 transition-all ${
                            activeTab === 'current' 
                              ? 'text-purple-400 border-b-2 border-purple-400' 
                              : 'text-gray-500 hover:text-gray-300'
                          }`}
                        >
                          <span>💖</span> CURRENT
                        </button>
                        <button 
                          onClick={() => setActiveTab('new')}
                          className={`px-4 py-2.5 text-xs font-semibold flex items-center gap-1.5 transition-all ${
                            activeTab === 'new' 
                              ? 'text-purple-400 border-b-2 border-purple-400' 
                              : 'text-gray-500 hover:text-gray-300'
                          }`}
                        >
                          <span>🤖</span> NEW
                        </button>
                        <button 
                          onClick={() => setActiveTab('messaging')}
                          className={`px-4 py-2.5 text-xs font-semibold flex items-center gap-1.5 relative transition-all ${
                            activeTab === 'messaging' 
                              ? 'text-purple-400 border-b-2 border-purple-400' 
                              : 'text-gray-500 hover:text-gray-300'
                          }`}
                        >
                          <span>💬</span> MESSAGING
                          <span className="absolute -top-1 -right-1 bg-gradient-to-r from-pink-500 to-purple-500 text-white text-[8px] px-1.5 py-0.5 rounded-full font-bold animate-pulse">NEW</span>
                        </button>
                        <div className="absolute bottom-0 left-0 w-full h-px bg-purple-500/20"></div>
                      </div>
                      
                      {/* Main content */}
                      <div style={{ height: '380px', overflowY: 'hidden' }}>
                        {renderContent()}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Decorative elements */}
              <div className="absolute -z-10 top-8 right-8 w-72 h-72 bg-purple-500/10 rounded-full blur-3xl"></div>
              <div className="absolute -z-10 bottom-8 left-8 w-72 h-72 bg-pink-500/10 rounded-full blur-3xl"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
} 
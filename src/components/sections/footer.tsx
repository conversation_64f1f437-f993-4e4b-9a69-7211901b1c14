"use client";
import Image from "next/image";
import Link from "next/link";
import { Mail, Twitter, Instagram } from 'lucide-react';

export function Footer() {
  return (
    <footer className="relative py-16 bg-gradient-to-br from-gray-900 to-black border-t border-purple-500/20">
      {/* Background decoration */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-0 left-1/4 w-96 h-96 bg-purple-600/10 rounded-full mix-blend-multiply filter blur-3xl"></div>
        <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-pink-600/10 rounded-full mix-blend-multiply filter blur-3xl"></div>
      </div>
      
      <div className="relative max-w-7xl mx-auto px-6 lg:px-8">
        <div className="grid md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="md:col-span-1">
            <Link href="/" className="flex items-center gap-3 hover:opacity-80 transition-opacity mb-4" aria-label="OF AutoFollower - Home">
              <Image 
                src="/OFAFlogo.png" 
                alt="OF AutoFollower Logo" 
                width={45} 
                height={45}
                className="w-11 h-11"
                priority={false}
              />
              <span className="text-xl font-bold bg-gradient-to-r from-white to-purple-200 bg-clip-text text-transparent">
                ofautofollower
              </span>
            </Link>
            <p className="text-gray-400 text-sm leading-relaxed">
              The most trusted automation tool for OnlyFans creators. Grow your subscriber base and maximize your revenue safely.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-white font-semibold mb-4">Quick Links</h3>
            <div className="space-y-2">
              <Link href="/#features" className="block text-gray-400 hover:text-purple-400 transition-colors text-sm">Features</Link>
              <Link href="/bot/pricing" className="block text-gray-400 hover:text-purple-400 transition-colors text-sm">Pricing</Link>
              <Link href="/#faq" className="block text-gray-400 hover:text-purple-400 transition-colors text-sm">FAQ</Link>
              <Link href="/affiliate" className="block text-gray-400 hover:text-purple-400 transition-colors text-sm">Affiliate Program</Link>
            </div>
          </div>

          {/* Legal */}
          <div>
            <h3 className="text-white font-semibold mb-4">Legal</h3>
            <div className="space-y-2">
              <Link href="/privacy-policy" className="block text-gray-400 hover:text-purple-400 transition-colors text-sm">Privacy Policy</Link>
              <Link href="/terms" className="block text-gray-400 hover:text-purple-400 transition-colors text-sm">Terms of Service</Link>
              <Link href="/refund" className="block text-gray-400 hover:text-purple-400 transition-colors text-sm">Refund Policy</Link>
            </div>
          </div>

          {/* Contact */}
          <div>
            <h3 className="text-white font-semibold mb-4">Get in Touch</h3>
            <div className="space-y-3">
              <a 
                href="mailto:<EMAIL>" 
                className="flex items-center gap-2 text-gray-400 hover:text-purple-400 transition-colors text-sm"
              >
                <Mail className="w-4 h-4" />
                <EMAIL>
              </a>
              <div className="flex gap-3 mt-4">
                <a 
                  href="https://twitter.com/ofautofollower" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="p-2 bg-white/5 rounded-lg hover:bg-purple-500/20 text-gray-400 hover:text-purple-400 transition-colors"
                  aria-label="Follow us on Twitter"
                >
                  <Twitter className="w-4 h-4" />
                </a>
                <a 
                  href="https://instagram.com/ofautofollower" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="p-2 bg-white/5 rounded-lg hover:bg-purple-500/20 text-gray-400 hover:text-purple-400 transition-colors"
                  aria-label="Follow us on Instagram"
                >
                  <Instagram className="w-4 h-4" />
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom section */}
        <div className="mt-12 pt-8 border-t border-purple-500/20">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <p className="text-gray-500 text-sm">
              &copy; {new Date().getFullYear()} ofautofollower.com. All rights reserved.
            </p>
            <p className="text-gray-500 text-sm">
              Made with ❤️ for OnlyFans creators
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
} 
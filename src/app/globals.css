@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 4%;
  --foreground: 0 0% 98%;
  --card: 270 4% 8%;
  --card-foreground: 0 0% 98%;
  --popover: 270 4% 8%;
  --popover-foreground: 0 0% 98%;
  --primary: 271 100% 50%;
  --primary-foreground: 0 0% 100%;
  --secondary: 270 8% 12%;
  --secondary-foreground: 0 0% 98%;
  --muted: 270 8% 16%;
  --muted-foreground: 270 8% 65%;
  --accent: 271 100% 45%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 85% 60%;
  --destructive-foreground: 0 0% 98%;
  --border: 270 8% 16%;
  --input: 270 8% 16%;
  --ring: 271 100% 60%;
  --radius: 0.75rem;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

/* Prevent media elements from causing horizontal overflow */
img, video, iframe {
  max-width: 100%;
  height: auto;
}

html {
  overflow-x: hidden;
  overflow-y: auto;
  scroll-behavior: smooth;
  font-feature-settings: 'cv11', 'ss01';
  font-variation-settings: 'opsz' 32;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(180deg, #0a0a0a 0%, #050505 100%);
  color: hsl(var(--foreground));
}

body {
  position: relative;
  min-height: 100vh;
}

@layer base {
  h1, h2, h3, h4, h5, h6 {
    font-feature-settings: 'cv11', 'ss01';
    font-variation-settings: 'opsz' 32;
    color: hsl(var(--foreground));
  }
}

@layer components {
  .text-balance {
    text-wrap: balance;
  }
  
  .gradient-bg {
    background: linear-gradient(135deg, #9500ff 0%, #b333ff 50%, #c666ff 100%);
  }
  
  .gradient-text {
    background: linear-gradient(135deg, #9500ff 0%, #ff00ff 50%, #00ffff 100%);
    background-size: 200% 200%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 8s ease-in-out infinite;
  }
  
  .subtle-gradient-text {
    background: linear-gradient(90deg, #9500ff 0%, #c666ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  .primary-glow {
    background: radial-gradient(circle at center, rgba(149, 0, 255, 0.15) 0%, transparent 70%);
  }
  
  @keyframes gradientShift {
    0%, 100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }
  
  @keyframes float {
    0%, 100% {
      transform: translateY(0px) scale(1);
    }
    50% {
      transform: translateY(-20px) scale(1.02);
    }
  }
  
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes glow {
    0%, 100% {
      box-shadow: 0 0 20px rgba(149, 0, 255, 0.4);
    }
    50% {
      box-shadow: 0 0 40px rgba(149, 0, 255, 0.6);
    }
  }
  
  @keyframes pulse-ring {
    0% {
      transform: scale(1);
      opacity: 1;
    }
    100% {
      transform: scale(1.5);
      opacity: 0;
    }
  }
  
  .animate-float {
    animation: float 6s ease-in-out infinite;
  }
  
  .animate-glow {
    animation: glow 3s ease-in-out infinite;
  }
  
  .animate-fadeInUp {
    animation: fadeInUp 0.8s ease-out forwards;
    opacity: 0;
  }
  
  .hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .hover-lift:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: 0 20px 40px rgba(149, 0, 255, 0.2);
  }
  
  .premium-button {
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: linear-gradient(135deg, #9500ff 0%, #b333ff 100%);
  }
  
  .premium-button:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.3),
      transparent
    );
    transition: left 0.6s;
  }
  
  .premium-button:hover:before {
    left: 100%;
  }
  
  .premium-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 15px 35px rgba(149, 0, 255, 0.3);
  }
  
  .glass-card {
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(149, 0, 255, 0.15);
  }
  
  .hero-gradient {
    background: radial-gradient(ellipse 80% 50% at 50% -20%, rgba(149, 0, 255, 0.25), transparent),
                radial-gradient(ellipse 60% 40% at 80% 50%, rgba(255, 0, 255, 0.15), transparent),
                linear-gradient(180deg, rgba(10, 10, 10, 0.95) 0%, rgba(5, 5, 5, 1) 100%);
  }
  
  .glass-effect {
    background: rgba(149, 0, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(149, 0, 255, 0.2);
  }
  
  .card-shadow {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.5), 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  }
  
  .card-shadow-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
  }
  
  .btn-primary {
    @apply bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white font-medium px-6 py-3 rounded-xl transition-all duration-200 inline-flex items-center justify-center gap-2 shadow-lg hover:shadow-purple-600/25;
  }
  
  .btn-secondary {
    @apply bg-gray-900/50 hover:bg-gray-800/50 text-white font-medium px-6 py-3 rounded-xl border border-purple-500/20 transition-all duration-200 inline-flex items-center justify-center gap-2 backdrop-blur-sm;
  }
  
  .section-bg {
    background: rgba(10, 10, 10, 0.6);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(149, 0, 255, 0.1);
    border-bottom: 1px solid rgba(149, 0, 255, 0.1);
  }
  
  .card-bg {
    background: rgba(15, 15, 15, 0.8);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(149, 0, 255, 0.15);
  }
  
  .neon-border {
    position: relative;
    border: 1px solid rgba(149, 0, 255, 0.5);
    box-shadow: 0 0 20px rgba(149, 0, 255, 0.3);
  }
  
  /* Promote AOS-animated elements to their own compositing layer so the GPU
     can handle the transforms efficiently and scrolling stays smooth. */
  [data-aos] {
    will-change: opacity, transform;
    /* Ensure elements are visible by default - IMPORTANT for fallback */
    opacity: 1 !important;
    transform: translate3d(0, 0, 0) !important;
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
  }

  /* Only apply initial hidden state when AOS is properly initialized */
  body.aos-init [data-aos="fade-up"]:not(.aos-animate) {
    transform: translate3d(0, 20px, 0) !important;
    opacity: 0 !important;
  }

  /* Animated state */
  [data-aos="fade-up"].aos-animate {
    transform: translate3d(0, 0, 0) !important;
    opacity: 1 !important;
  }
}

import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import "aos/dist/aos.css";
import { AOSInit } from "@/components/aos-init";
import Script from "next/script";

const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
  preload: true,
});

export const metadata: Metadata = {
  title: "OF Auto Follower - Recover Expired Fans & Grow Your OnlyFans",
  description: "Auto-follow expired fans and new OF users to maximize your revenue. The #1 growth automation tool for OnlyFans creators. Recover lost earnings with one click.",
  keywords: "onlyfans auto follower, of auto follower, onlyfans expired fans, onlyfans automation, onlyfans growth tool, onlyfans bot, expired fans recovery",
  openGraph: {
    title: "OF Auto Follower - Recover Expired Fans & Grow Your OnlyFans",
    description: "The ultimate OnlyFans automation tool. Auto-follow expired fans and new users instantly. Maximize your revenue and grow your subscriber base. 100% SAFE.",
    url: "https://ofautofollower.com/",
  },
  icons: {
    icon: '/favicon.png',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export const viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={`overflow-x-hidden ${inter.variable}`}>
      <body className={`${inter.className} antialiased`}>
        {/* Immediate visibility fallback script */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // Ensure sections are visible immediately as a fallback
              (function() {
                function ensureVisibility() {
                  try {
                    const elements = document.querySelectorAll('[data-aos]');
                    elements.forEach(function(el) {
                      el.style.setProperty('opacity', '1', 'important');
                      el.style.setProperty('transform', 'translate3d(0, 0, 0)', 'important');
                    });
                  } catch (e) {
                    console.warn('Fallback visibility script failed:', e);
                  }
                }

                // Run immediately
                ensureVisibility();

                // Run after DOM is ready
                if (document.readyState === 'loading') {
                  document.addEventListener('DOMContentLoaded', ensureVisibility);
                } else {
                  ensureVisibility();
                }

                // Additional fallback after a short delay
                setTimeout(ensureVisibility, 100);
              })();
            `,
          }}
        />
        <AOSInit />
        {children}
        
        {/* Google Analytics - Moved to end of body for better performance */}
        {/* <Script
          src="https://www.googletagmanager.com/gtag/js?id=UA-143287123-10"
          strategy="afterInteractive"
        />
        <Script id="google-analytics" strategy="afterInteractive">
          {`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', 'UA-143287123-10');
            gtag('config', 'AW-320170390');
          `}
        </Script> */}
        
        {/* Plausible Analytics */}
        <Script 
          defer 
          data-domain="ofautofollower.com" 
          src="https://plausible.io/js/plausible.js"
          strategy="afterInteractive"
        />
      </body>
    </html>
  );
}
